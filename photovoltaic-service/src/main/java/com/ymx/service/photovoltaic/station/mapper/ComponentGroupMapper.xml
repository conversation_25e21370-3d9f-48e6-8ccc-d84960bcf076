<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ymx.service.photovoltaic.station.mapper.ComponentGroupMapper">

    <!-- 查询对象数据列表 -->
    <select id="queryComponentGroupById" parameterType="java.lang.String" resultType="ComponentGroupModel">
        SELECT
             e.*,
            (select inverterName from t_app_inverter v where v.id = e.inverterId) as inverterName ,
            (case when e.createType = 1 then
                (select user_name from t_fs_user mi where mi.id = e.createUserId)
            else
                (select CONCAT(mi.xing , mi.name) from t_app_member_info mi where mi.m_id = e.createUserId)
            end ) as createUserName
        FROM
        t_app_component_group e
        WHERE 1 = 1
        AND isDelete = 1
        and id = #{id}
    </select>


    <select id="queryGroupById" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT id from t_app_component_group where id = #{id}
    </select>


    <select id="queryComponentGroupInfo" resultType="ComponentGroupModel">
        select belongsGroupId as id ,count(id) as groupNum,powerStationId from t_app_component
        where belongsGroupId is not null group by belongsGroupId,powerStationId
    </select>

    <select id="queryGroupByPowerIdList" resultType="ComponentGroupModel">
        select belongsGroupId as id ,count(id) as groupNum,powerStationId from t_app_component
        where belongsGroupId is not null
        and powerStationId in
        <foreach item="item" index="index" collection="list" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        group by belongsGroupId,powerStationId
    </select>

    <!-- 查询组串数据列表 -->
    <select id="queryComponentGroupList" parameterType="HashMap" resultType="ComponentGroupModel">
        SELECT
	    	(select count(1) from t_app_component where isDelete = 1 and e.id=belongsGroupId) as groupNum,
           e.* ,
           (select inverterName from t_app_inverter v where v.id = e.inverterId) as inverterName ,
	    	(select cloudName from t_app_cloudterminal  where id = e.cloudId) as cloudName ,
           (case when e.createType = 1 then
              (select user_name from t_fs_user mi where mi.id = e.createUserId)
            else
              (select CONCAT(mi.xing , mi.name) from t_app_member_info mi where mi.m_id = e.createUserId)
            end ) as createUserName,
	    	powerstation.systemName as powerStationName
        FROM
        t_app_component_group e
	    left join t_app_powerstation powerstation on powerstation.id=e.powerStationId
        WHERE 1 = 1
        AND isDelete = 1
	    <if test="null != powerStationName and '' != powerStationName">
		    AND powerstation.systemName like CONCAT('%' , #{powerStationName}, '%')
	    </if>
	    <if test="null != powerStationId and '' != powerStationId">
		    AND e.powerStationId = #{powerStationId}
	    </if>
        <if test="null != groupName and '' != groupName">
            AND e.groupName like CONCAT('%' , #{groupName} , '%')
        </if>
        <!-- 创建时间 -->
        <if test="createTimeEnd != null and createTimeEnd != ''">
            AND DATE_FORMAT(e.createTime ,'%Y-%m-%d %H:%i:%s') &lt;= #{createTimeEnd}
        </if>
        <if test="createTimeBegin != null and createTimeBegin != ''">
            AND DATE_FORMAT(e.createTime ,'%Y-%m-%d %H:%i:%s') &gt;= #{createTimeBegin}
        </if>
	    <if test="null != inverterFlag and 'detailComponentGroup' == inverterFlag">
		    AND e.inverterId = #{inverterId}
	    </if>
        <if test="null != inverterFlag and 'selectComponentGroup' == inverterFlag">
            AND e.inverterId is null
        </if>

	    <if test="null != cloudIdFlag and 'addComponent' == cloudIdFlag">
		    AND e.cloudId is null
	    </if>
	    <if test="null != cloudIdFlag and 'selectComponent' == cloudIdFlag">
		    AND e.cloudId =#{cloudId}
	    </if>
       <if test="createUserId != null and createUserId != ''">
            AND e.createUserId = #{createUserId}
        </if>

        <!-- 排序 -->
        order by e.createTime DESC
        <!-- limit为-1时查询所有数据 -->
        <if test="null != page and page.pageSize != -1">
            limit #{page.next},#{page.pageSize}
        </if>
    </select>

    <!-- 查询组串总记录 -->
    <select id="queryComponentGroupListCount" parameterType="HashMap" resultType="java.lang.Integer">
        SELECT
          count(1) as counts
        FROM
        t_app_component_group e
	    left join t_app_powerstation powerstation on powerstation.id=e.powerStationId
        WHERE 1 = 1
        AND isDelete = 1
	    <if test="null != powerStationName and '' != powerStationName">
		    AND powerstation.systemName like CONCAT('%' , #{powerStationName}, '%')
	    </if>
	    <if test="null != powerStationId and '' != powerStationId">
		    AND e.powerStationId = #{powerStationId}
	    </if>
        <if test="null != groupName and '' != groupName">
            AND e.groupName like CONCAT('%' , #{groupName} , '%')
        </if>
        <!-- 创建时间 -->
        <if test="createTimeEnd != null and createTimeEnd != ''">
            AND DATE_FORMAT(e.createTime ,'%Y-%m-%d %H:%i:%s') &lt;= #{createTimeEnd}
        </if>
        <if test="createTimeBegin != null and createTimeBegin != ''">
            AND DATE_FORMAT(e.createTime ,'%Y-%m-%d %H:%i:%s') &gt;= #{createTimeBegin}
        </if>
        <if test="null != inverterFlag and 'detailComponentGroup' == inverterFlag">
            AND e.inverterId = #{inverterId}
        </if>
        <if test="null != inverterFlag and 'selectComponentGroup' == inverterFlag">
            AND e.inverterId is null
        </if>
	   <if test="null != cloudIdFlag and 'addComponent' == cloudIdFlag">
		    AND e.cloudId is null
	    </if>
	    <if test="null != cloudIdFlag and 'selectComponent' == cloudIdFlag">
		    AND e.cloudId =#{cloudId}
	    </if>
	     <if test="createUserId != null and createUserId != ''">
            AND e.createUserId = #{createUserId}
        </if>
    </select>

    <!-- 保存组串数据 -->
    <insert id="saveComponentGroupModel" parameterType="ComponentGroupModel" >
        insert into t_app_component_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                id ,
            </if>
	        <if test="null != powerStationId and '' != powerStationId">
		        powerStationId ,
	        </if>
	        <if test="null != cloudId and '' != cloudId">
		        cloudId ,
	        </if>
            <if test="null != inverterId and '' != inverterId">
                inverterId ,
            </if>
            <if test="null != groupName and '' != groupName">
                groupName ,
            </if>
            <if test="null != groupNo and '' != groupNo">
                groupNo ,
            </if>
            <if test="null != groupNum and 0 != groupNum">
                groupNum ,
            </if>
            <if test="null != groupType and 0 != groupType">
                groupType ,
            </if>
            <if test="null != createUserId and '' != createUserId">
                createUserId ,
            </if>
            <if test="null != createType and 0 != createType">
                createType ,
            </if>
	        <if test="null != power and 0 != power">
		        power ,
	        </if>
	        <if test="null != chipId and '' != chipId">
		        chipId ,
	        </if>
            createTime,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                #{id} ,
            </if>
	        <if test="null != powerStationId and '' != powerStationId">
		        #{powerStationId} ,
	        </if>
	        <if test="null != cloudId and '' != cloudId">
		        #{cloudId} ,
	        </if>
            <if test="null != inverterId and '' != inverterId">
                #{inverterId} ,
            </if>
            <if test="null != groupName and '' != groupName">
                #{groupName} ,
            </if>
            <if test="null != groupNo and '' != groupNo">
                #{groupNo} ,
            </if>
            <if test="null != groupNum and 0 != groupNum">
                #{groupNum} ,
            </if>
            <if test="null != groupType and 0 != groupType">
                #{groupType} ,
            </if>
            <if test="null != createUserId and '' != createUserId">
                #{createUserId} ,
            </if>
            <if test="null != createType and 0 != createType">
                #{createType} ,
            </if>
	        <if test="null != power and 0 != power">
		        #{power} ,
	        </if>
	        <if test="null != chipId and '' != chipId">
		        #{chipId} ,
	        </if>
            NOW(),
        </trim>
    </insert>

    <insert id="saveGroupPosition">
        insert into t_group_position
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                id ,
            </if>
            <if test="null != powerStationId and '' != powerStationId">
                powerId ,
            </if>
            <if test="null != groupId and '' != groupId">
                groupId ,
            </if>
            <if test="null != divleft and '' != divleft">
                divleft ,
            </if>
            <if test="null != divtop and '' != divtop">
                divtop ,
            </if>
            <if test="null != width and '' != width">
                width ,
            </if>
            <if test="null != height and 0 != height">
                height ,
            </if>
            <if test="null != lb and 0 != lb">
                lb ,
            </if>
            <if test="null != xz and '' != xz">
                xz ,
            </if>
            <if test="null != yz and 0 != yz">
                yz ,
            </if>
            <if test="null != laystatus and 0 != laystatus">
                laystatus ,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                #{id} ,
            </if>
            <if test="null != powerStationId and '' != powerStationId">
                #{powerStationId} ,
            </if>
            <if test="null != groupId and '' != groupId">
                #{groupId} ,
            </if>
            <if test="null != divleft and '' != divleft">
                #{divleft} ,
            </if>
            <if test="null != divtop and '' != divtop">
                #{divtop} ,
            </if>
            <if test="null != width and '' != width">
                #{width} ,
            </if>
            <if test="null != height and 0 != height">
                #{height} ,
            </if>
            <if test="null != lb and 0 != lb">
                #{lb} ,
            </if>
            <if test="null != xz and '' != xz">
                #{xz} ,
            </if>
            <if test="null != yz and 0 != yz">
                #{yz} ,
            </if>
            <if test="null != laystatus and 0 != laystatus">
                #{laystatus} ,
            </if>
        </trim>

    </insert>

    <!-- 修改组串 -->
    <update id="updateComponentGroupModel" parameterType="ComponentGroupModel">
        update t_app_component_group
        <set>
	        <if test="null != powerStationId and '' != powerStationId">
		        powerStationId = #{powerStationId} ,
	        </if>
            <if test="null != inverterId and '' != inverterId">
                inverterId = #{inverterId} ,
            </if>
	        <if test="null != cloudId and '' != cloudId">
		        cloudId = #{cloudId} ,
	        </if>
            <if test="null != groupName and '' != groupName">
                groupName = #{groupName} ,
            </if>
            <if test="null != groupNo and '' != groupNo">
                groupNo = #{groupNo} ,
            </if>
            <if test="null != groupType and 0 != groupType">
                groupType = #{groupType} ,
            </if>
            <if test="null != groupNum and 0 != groupNum">
                groupNum = #{groupNum} ,
            </if>
	        <if test="null != power and 0 != power">
		        power = #{power} ,
	        </if>
	        <if test="null != chipId and '' != chipId">
		        chipId = #{chipId} ,
	        </if>
        </set>
        where 1 = 1
        AND id = #{id}
    </update>

    <!-- 删除 -->
    <delete id="deleteComponentGroupModels" parameterType="HashMap">
        DELETE FROM t_app_component_group WHERE ID IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <!-- 逻辑删除 -->
    <update id="updateDeleteComponentGroupModels" parameterType="HashMap">
        update t_app_component_group
        SET isDelete = 2
        WHERE ID IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>


    <!-- 从逆变器 修改(移除)  -->
    <update id="updateExistsInverer" parameterType="HashMap" >
        update t_app_component_group
        <set>
            <if test="null == inverterId or '' == inverterId">
                inverterId = null ,
            </if>
            <if test="null != inverterId and '' != inverterId">
                inverterId = #{inverterId} ,powerStationId = #{powerStationId} ,
            </if>
            <if test="(null == cloudId or '' == cloudId) &amp;&amp; (null == inverterId or '' == inverterId) ">
				powerStationId = null
			</if>
        </set>
        WHERE id = #{id}
        <!-- <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach> -->
    </update>
	<!-- 从采集器 修改(移除)  -->
	<update id="updateCloudExistsInverer" parameterType="HashMap" >
		update t_app_component_group
		<set>
			<if test="null == cloudId or '' == cloudId">
				cloudId = null ,
			</if>
			<if test="null != cloudId and '' != cloudId">
				cloudId = #{cloudId} ,powerStationId = #{powerStationId} ,
			</if>
			<if test="(null == inverterId or '' == inverterId) &amp;&amp; (null == cloudId or '' == cloudId)">
                powerStationId = null,
            </if>
			<if test="null != powerStationIdFlag and '' != powerStationIdFlag">
				powerStationId = null ,
			</if>
		</set>
		WHERE id = #{id}
		<!-- <foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach> -->
	</update>
	<update id="updatePowerStationId" parameterType="ComponentGroupModel" >
		update t_app_component_group
		set powerStationId = null
		WHERE powerStationId IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</update>



    <!-- 查询是否在 芯片的ID， 组件唯一标示  -->
    <select id="queryComponentGroupListByData" resultType="ComponentGroupModel" parameterType="HashMap">
        select c.*,a.xz as xz,a.yz as yz ,a.lb as position,a.divtop AS divtop,
			a.divleft AS divleft,a.qb AS qb,a.laystatus AS laystatus from t_app_component_group c
	    LEFT JOIN t_app_inverter i on c.inverterId = i.id
	    LEFT JOIN t_app_change a ON a.zjid = c.id
	    where  1= 1 and c.isDelete = 1 
        <if test="null != powerStationId and '' != powerStationId">
           AND i.powerStationId = #{powerStationId}
        </if>
        <if test="null != ymd and '' != ymd">
             AND DATE_FORMAT(c.createTime ,'%Y-%m-%d') = #{ymd}
        </if>
    </select>

<!-- 查询对象数据列表 -->
    <select id="queryComponentGroupByCloudId" parameterType="java.lang.String" resultType="ComponentGroupModel">
        SELECT
             e.*,
            (select inverterName from t_app_inverter v where v.id = e.inverterId) as inverterName ,
            (case when e.createType = 1 then
                (select user_name from t_fs_user mi where mi.id = e.createUserId)
            else
                (select CONCAT(mi.xing , mi.name) from t_app_member_info mi where mi.m_id = e.createUserId)
            end ) as createUserName
        FROM
        t_app_component_group e
        WHERE 1 = 1
        AND isDelete = 1
        and cloudId = #{id}
    </select>
<!-- 查询对象数据列表 -->
    <select id="queryvalidatecomponentGroup" parameterType="HashMap" resultType="ComponentGroupModel">
        SELECT
             e.*
        FROM
        t_app_component_group e
        WHERE 1 = 1
        AND isDelete = 1
       <if test="null != groupNo and '' != groupNo">
              and  groupNo = #{groupNo}
            </if>
	    <if test="null != id and '' != id">
		    and  id = #{id}
	    </if>
    </select>
    <!-- 查询对象数据列表 -->
    <select id="queryListByCloudId" parameterType="java.lang.String" resultType="ComponentGroupModel">
        SELECT
             e.* ,
           (select inverterName from t_app_inverter v where v.id = e.inverterId) as inverterName ,
           (case when e.createType = 1 then
              (select user_name from t_fs_user mi where mi.id = e.createUserId)
            else
              (select CONCAT(mi.xing , mi.name) from t_app_member_info mi where mi.m_id = e.createUserId)
            end ) as createUserName,
	    (select count(1) from t_app_component where isDelete = 1 and e.id=belongsGroupId) as groupNum

        FROM
        t_app_component_group e
        WHERE 1 = 1
        AND isDelete = 1
        and cloudId = #{id}
    </select>
    <!-- 查询对象数据列表 -->
    <select id="queryListByInverterId" parameterType="java.lang.String" resultType="ComponentGroupModel">
        SELECT
             e.* ,
           (select inverterName from t_app_inverter v where v.id = e.inverterId) as inverterName ,
           (case when e.createType = 1 then
              (select user_name from t_fs_user mi where mi.id = e.createUserId)
            else
              (select CONCAT(mi.xing , mi.name) from t_app_member_info mi where mi.m_id = e.createUserId)
            end ) as createUserName,
	    (select count(1) from t_app_component where isDelete = 1 and e.id=belongsGroupId) as groupNum

        FROM
        t_app_component_group e
        WHERE 1 = 1
        AND isDelete = 1
        and inverterId = #{id}
    </select>
    <!-- 查询关联好的组串 -->
    <select id="querycomponentGroupByPowerId" parameterType="java.lang.String" resultType="ComponentGroupModel">
        SELECT
             e.*
        FROM
        t_app_component_group e
        WHERE 1 = 1
        AND isDelete = 1 and (e.inverterId is not null or e.cloudId is not null )
		    and  powerStationId = #{powerid}
    </select>
	<!-- 查询关联好的组串 -->
	<select id="queryComponentGroupRepeatList" parameterType="ComponentGroupModel" resultType="ComponentGroupModel">
		SELECT
		e.*
		FROM
		t_app_component_group e
		WHERE 1 = 1
		AND isDelete = 1
		<if test="null != chipId and '' != chipId">
			AND e.chipId = #{chipId}
		</if>
		<if test="null != id and '' != id">
			AND e.id != #{id}
		</if>
	</select>
	<!-- 查询关联好的组串 -->
	<select id="queryComponentGroupInverterList"  resultType="ComponentGroupModel">
		SELECT
		e.*
		FROM
		t_app_component_group e
		WHERE 1 = 1
		AND e.isDelete = 1 AND e.inverterId = #{inverterId}
	</select>


    <!-- 根据创建者id查询组串名称和id -->
    <select id="queryGroupForImportComponent"  resultType="ComponentGroupModel" parameterType="java.lang.String">
        SELECT groupName,id FROM t_app_component_group WHERE createUserId = #{createUserId}
    </select>

    <select id="queryGroupIdAndName"  resultType="GroupPowerModel">
        select id as groupId,groupName from t_app_component_group
        where powerStationId = #{powerStationId}
        <if test="null != groupName and '' != groupName">
            and groupName like '%${groupName}%'
        </if>

    </select>

    <!-- 查询组串位置 查询9个字段-->
    <select id="queryGroupPosition" resultType="ComponentGroupModel" parameterType="java.lang.String">
        SELECT p.xz , p.yz, p.divtop, p.divleft, p.lb as position, p.laystatus,g.id,g.powerStationId,g.groupName from
        t_app_component_group g left join t_group_position p on p.groupId=g.id where g.powerStationId=#{powerStationId}
    </select>

    <!-- 查询组串位置信息 -->
    <select id="queryGroupPositionForApp" resultType="ShowGroupModel" parameterType="java.lang.String">
        SELECT p.xz as gapLeft, p.yz as gapTop, p.lb as hv,p.groupId,g.groupName,g.power
        FROM t_group_position p,t_app_component_group g
        where p.groupId=g.id and p.powerid = #{powerStationId}
    </select>

    <select id="queryGroupInfoForApp" resultType="GroupModel" parameterType="java.lang.String">
        SELECT id as groupId,groupName,power FROM t_app_component_group where powerStationId = #{powerStationId}
    </select>

    <select id="queryGroupInfoForWeb" resultType="GroupModel" parameterType="java.lang.String">
        SELECT g.id as groupId,g.groupName,power,p.*
        FROM t_app_component_group g
        left join t_group_position p
        on g.id = p.groupId
        where g.powerStationId = #{powerStationId}
    </select>

    <select id="queryGroupByKwh"
            resultType="com.ymx.service.photovoltaic.station.model.PowerStationGroupByKwh"
            parameterType="map">
        select c.kwh,c.powerStationId
        from t_powerstation_collect c
        where c.powerStationId in
        <foreach collection="powerStationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and createTime > #{startOfDay}
    </select>


    <select id="queryGroupInfo" resultType="com.ymx.service.photovoltaic.station.model.GroupModel" parameterType="map">
        SELECT
        e.id as groupId,e.powerStationId,e.power,e.groupName,
        p.divleft,p.divtop,p.width,p.height,p.lb,p.xz,p.yz,p.laystatus
        FROM t_app_component_group e,t_group_position p
        WHERE 1 = 1
        AND e.isDelete = 1
        AND e.id = p.groupId
        <if test="null != powerStationId and '' != powerStationId">
            and e.powerStationId = #{powerStationId}
        </if>
        <if test="null != groupId and '' != groupId">
            and e.id = #{groupId}
        </if>
    </select>

    <select id="queryGroupPositionById" resultType="java.lang.String">
        select groupId from t_group_position where groupId = #{groupId}
    </select>

    <select id="queryPositionByGroupId" resultType="java.lang.String">
        select id from t_group_position where groupId = #{groupId}
    </select>

    <update id="updateGroupPosition">
        update t_group_position
        <set>
            <if test="null != powerStationId and '' != powerStationId">
                powerId = #{powerStationId} ,
            </if>
            <if test="null != groupId and '' != groupId">
                groupId = #{groupId} ,
            </if>
            <if test="null != divleft and '' != divleft">
                divleft = #{divleft} ,
            </if>
            <if test="null != divtop and '' != divtop">
                divtop = #{divtop} ,
            </if>
            <if test="null != width and '' != width">
                width = #{width} ,
            </if>
            <if test="null != height and 0 != height">
                height = #{height} ,
            </if>
            <if test="null != lb and 0 != lb">
                lb = #{lb} ,
            </if>
            <if test="null != xz and 0 != xz">
                xz = #{xz} ,
            </if>
            <if test="null != yz and '' != yz">
                yz = #{yz} ,
            </if>
            <if test="null != laystatus and '' != laystatus">
                laystatus = #{laystatus} ,
            </if>
        </set>
        where 1 = 1
        AND groupId = #{groupId}
    </update>

</mapper>