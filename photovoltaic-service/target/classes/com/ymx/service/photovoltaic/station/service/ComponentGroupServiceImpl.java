package com.ymx.service.photovoltaic.station.service;

import com.ymx.common.common.constant.ErrCode;
import com.ymx.common.common.result.CallResult;
import com.ymx.common.common.result.PageInfo;
import com.ymx.common.common.result.PageView;
import com.ymx.common.utils.*;
import com.ymx.service.cache.Configure;
import com.ymx.service.constant.ErrCodeExt;
import com.ymx.service.constant.TranslateLanguage;
import com.ymx.service.photovoltaic.station.mapper.*;
import com.ymx.service.photovoltaic.station.model.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.InputStream;
import java.util.*;

/**
 * @DESC 组串服务层
 * @DATE 2018/8/11
 * @NAME ComponentGroupServiceImpl
 * @MOUDELNAME 模块
 */
@Service
public class ComponentGroupServiceImpl implements ComponentGroupService {

    @Resource
    private ComponentGroupMapper componentGroupMapper;
    @Resource
    private CloudTerminalMapper cloudTerminalMapper;
	@Resource
	private InverterMapper inverterMapper;

	@Resource
	private ComponentMapper componentMapper;
	@Resource
	private TestMapper testMapper;

	/***
	 * 查询组件数据
	 * @param model    组串
	 * @param pageView 分页实体
	 * @return
	 */
	@Override
	public CallResult queryTestModelList(TestModel model, PageView pageView) {
		CallResult callResult = CallResult.newInstance();

		Map<String, Object> map = new HashMap<String, Object>();
		QueryParamMapUtil.setMapPropreties(map, model, true);
		int count = testMapper.queryTestModelCount(map);
		PageInfo pageInfo = CommonPage.getPageInfo(count, pageView , "");
		map.put("page", pageInfo);
		callResult.setPageInfo(pageInfo);
		List<TestModel> list = testMapper.queryTestModelList(map);
		//清空map
		map.clear();
		if (!list.isEmpty()) {
			CommonUtil.hanldList(list);
		}
		map.put("data", list);
		callResult.setCount(count);
		callResult.setReModel(map);
		return callResult;
	}

	/***
     * 通过id 查询组串
     * @param id  组串id
     * @return
     */
    @Override
    public ComponentGroupModel queryComponentGroupById(String id) {
        ComponentGroupModel componentGroupModel = componentGroupMapper.queryComponentGroupById(id);
        if (null != componentGroupModel) {
            CommonUtil.hanldEntity(componentGroupModel);
        }
        return componentGroupModel;
    }

    /**
     * 新增
     * @param model 组串
     * @return
     */
    @Override
    public CallResult saveComponentGroupModel(ComponentGroupModel model) {
        CallResult callResult = CallResult.newInstance();
        // 主键
        model.setId(IDUtil.getUUIDStr());
	    // if(model.getChipId()!=null && model.getChipId().trim().length()>0){
		//     List<ComponentGroupModel>list= componentGroupMapper.queryComponentGroupRepeatList(model);
		//     if(list!=null && list.size()>0){
		// 	    TranslateLanguage.getCallResult(callResult,model.getLanguage(),ErrCode.GROUP_REPEAT);
		// 	    return callResult;
		//     }
	    // }
        int row  = componentGroupMapper.saveComponentGroupModel(model);
	    callResult.setReModel(model.getId());
        if (0 ==  row) {
//	        if("en".equals(model.getLanguage())){
//		        callResult.setErr( ErrCodeExt.INSERT_ERRO , ErrCodeExt.INSERT_ERRO_TEXT_EN );
//	        }else{
//		        callResult.setErr( ErrCodeExt.INSERT_ERRO , ErrCodeExt.INSERT_ERRO_TEXT );
//	        }
	        TranslateLanguage.getCallResult(callResult,model.getLanguage(), ErrCode.INSERT_ERRO);
            return callResult;
        }
        return callResult;
    }

    /**
     * 修改
     * @param model 组串
     * @return
     */
    @Override
    public CallResult updateComponentGroupModel(ComponentGroupModel model) {
        CallResult callResult = CallResult.newInstance();
        if (!CommonUtil.isEmpty(model.getId()) || !StringUtils.hasLength(model.getId())) {
//	        if("en".equals(model.getLanguage())){
//		        callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_EN );
//	        }else{
//		        callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT );
//	        }
	        TranslateLanguage.getCallResult(callResult,model.getLanguage(), ErrCodeExt.PARAM_ERR);
            return callResult;
        }
	    if(model.getChipId()!=null && model.getChipId().trim().length()>0){
		    List<ComponentGroupModel>list= componentGroupMapper.queryComponentGroupRepeatList(model);
		    if(list!=null && list.size()>0){
			    TranslateLanguage.getCallResult(callResult,model.getLanguage(),ErrCode.GROUP_REPEAT);
			    return callResult;
		    }
	    }
        componentGroupMapper.updateComponentGroupModel(model);
        return callResult;
    }

    /**
     * 删除
     * @param  model  组串
     * @return CallResult
     */
    @Override
    public CallResult deleteComponentGroups(ComponentGroupModel model) {
        CallResult callResult = CallResult.newInstance();
        Map<String , Object> map = new HashMap<>();
        if (CommonUtil.isEmpty(model.getId())) {
            List<String> list = new ArrayList<>();
            Collections.addAll(list, model.getId().split(",", -1));
            map.put("list", list);
	        for(String id:list){
		        ComponentGroupModel componentGroupModel=componentGroupMapper.queryComponentGroupById(id);
		        if(componentGroupModel!=null && componentGroupModel.getPowerStationId()!=null && componentGroupModel.getPowerStationId().length()>0){//删除缓存
			        Map<String, List<ComponentModel>> componentMap= Configure.getComponentMap();
			        componentMap.remove(componentGroupModel.getPowerStationId());
		        }
	        }
            int row  = componentGroupMapper.deleteComponentGroupModels(map);
            if (0 == row) {
//	            if("en".equals(model.getLanguage())){
//		            callResult.setErr( ErrCodeExt.DELETE_ERRO , ErrCodeExt.DELETE_ERRO_TEXT_EN );
//	            }else{
//		            callResult.setErr( ErrCodeExt.DELETE_ERRO , ErrCodeExt.DELETE_ERRO_TEXT );
//	            }
	            TranslateLanguage.getCallResult(callResult,model.getLanguage(), ErrCodeExt.DELETE_ERRO);
                return callResult;
            }
        }
        return callResult;
    }

    /**
     * 逻辑删除
     * @param  model  组串
     * @return CallResult
     */
    @Override
    public CallResult updateDeleteComponentGroupModels(ComponentGroupModel model) {
        CallResult callResult = CallResult.newInstance();
        Map<String , Object> map = new HashMap<>();
        if (CommonUtil.isEmpty(model.getId())) {
            List<String> list = new ArrayList<>();
            Collections.addAll(list, model.getId().split(",", -1));
            map.put("list", list);
            map.put("model" , model);
	        for(String id:list){
		        ComponentGroupModel componentGroupModel=componentGroupMapper.queryComponentGroupById(id);
		        if(componentGroupModel!=null && componentGroupModel.getPowerStationId()!=null && componentGroupModel.getPowerStationId().length()>0){//删除缓存
			        Map<String, List<ComponentModel>> componentMap= Configure.getComponentMap();
			        componentMap.remove(componentGroupModel.getPowerStationId());
		        }
	        }
            int row  = componentGroupMapper.updateDeleteComponentGroupModels(map);
            if (0 == row) {
//	            if("en".equals(model.getLanguage())){
//		            callResult.setErr( ErrCodeExt.DELETE_ERRO , ErrCodeExt.DELETE_ERRO_TEXT_EN );
//	            }else{
//		            callResult.setErr( ErrCodeExt.DELETE_ERRO , ErrCodeExt.DELETE_ERRO_TEXT );
//	            }
	            TranslateLanguage.getCallResult(callResult,model.getLanguage(), ErrCodeExt.DELETE_ERRO);
                return callResult;
            }
            //删除组件关系
            for(String id:list){
	            Map<String , Object> mapPs = new HashMap<>();
	            mapPs.put("cloudIdFlag" , "-1");
	            mapPs.put("belongsGroupId" , id);
	            mapPs.put("belongsGroupIdFlag" , "-1");
	            componentMapper.updatePowerStationByGroupId(mapPs);
            }
        }
        return callResult;
    }

    /***
     * 查询组件数据
     * @param model    组串
     * @param pageView 分页实体
     * @return
     */
    @Override
    public CallResult queryComponentGroupList(ComponentGroupModel model, PageView pageView) {
        CallResult callResult = CallResult.newInstance();
       
        Map<String, Object> map = new HashMap<String, Object>();
        QueryParamMapUtil.setMapPropreties(map, model, true);
        int count = componentGroupMapper.queryComponentGroupListCount(map);
        PageInfo pageInfo = CommonPage.getPageInfo(count, pageView , "");
        map.put("page", pageInfo);
        callResult.setPageInfo(pageInfo);
        List<ComponentGroupModel> list = componentGroupMapper.queryComponentGroupList(map);
        //清空map
        map.clear();
        if (!list.isEmpty()) {
            CommonUtil.hanldList(list);
        }
        map.put("data", list);
        callResult.setCount(count);
        callResult.setReModel(map);
        return callResult;
    }

    /**
     * 逆变器中选择组串/逆变器中移除组串
     * @param model  组串
     * @return
     */
    @Override
    @Transactional
    public CallResult selectOrMoveComponentGroup(ComponentGroupModel model) {
        CallResult callResult = CallResult.newInstance();
        Map<String ,Object> map2 = null;
        if (!StringUtils.hasLength(model.getId()) ) {
//	        if("en".equals(model.getLanguage())){
//		        callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_EN );
//	        }else{
//		        callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT );
//	        }
	        TranslateLanguage.getCallResult(callResult,model.getLanguage(), ErrCodeExt.PARAM_LOSS_EXCEPTION);
            return callResult;
        }
        String powerStationId="";
        if(CommonUtil.isEmpty(model.getInverterId())==true){
	        InverterModel inverterModel=inverterMapper.queryInverterById(model.getInverterId(),null);
	        if(inverterModel!=null){
		        powerStationId=inverterModel.getPowerStationId();
	        }
        }
	    String[] ids =  model.getId().split(",", -1);
        for(int i=0;i<ids.length;i++)
		 {
			map2 = new HashMap<>();
			String id = ids[i].toString();
		    ComponentGroupModel cgm =	componentGroupMapper.queryComponentGroupById(id);
			if(cgm!=null && cgm.getPowerStationId()!=null && cgm.getPowerStationId().length()>0){//删除缓存
				 Map<String, List<ComponentModel>> componentMap= Configure.getComponentMap();
				 componentMap.remove(cgm.getPowerStationId());
			}
		    String cloudid =null;
		    String inverid = null;
		    String powerid = null;
			 if(CommonUtil.isEmpty(model.getInverterId())==true){//选择组串
				 powerid=powerStationId;
				 inverid=model.getInverterId();
			 }else{//移除组串
				 cloudid=cgm.getCloudId();
			 }
		     map2.put("id", id);
		     map2.put("inverterId", inverid);
		     map2.put("powerStationId", powerid);
		     map2.put("cloudId", cloudid);
		     int row  = componentGroupMapper.updateExistsInverer(map2);
			 if(CommonUtil.isEmpty(model.getInverterId())==true) {//选择组串
				 if(CommonUtil.isEmpty(powerid)==true) {//采集器存在电站Id 组串不存在电站ID // && CommonUtil.isEmpty(cgm.getPowerStationId())==false
					 //修改组件 的电站ID
					 Map<String ,Object> mapP = new HashMap<>();
					 mapP.put("powerStationId",powerid);
					 mapP.put("belongsGroupId",id);
					 componentMapper.updatePowerStationByGroupId(mapP);
				 }
			 }else{
				 //修改组件 的电站ID
				 Map<String ,Object> mapP = new HashMap<>();
//				 mapP.put("powerStationId",powerid);
				 mapP.put("belongsGroupId",id);
				 if(cgm.getCloudId()!=null){
					 CloudTerminalModel cloudTerminalModel=  cloudTerminalMapper.queryCloudTerminalModelById(cgm.getCloudId());
					 if(cloudTerminalModel!=null && cloudTerminalModel.getPowerStationId()!=null && cloudTerminalModel.getPowerStationId().length()>0){
						 mapP.put("powerStationId",cloudTerminalModel.getPowerStationId());
					 }
				 }
				 componentMapper.updatePowerStationByGroupId(mapP);
			 }
		 }
        return callResult;
    }
	/**
	 * 采集器中选择组串/采集器中移除组串
	 * @param model  组串
	 * @return
	 */
	@Override
	@Transactional
	public CallResult selectOrMoveCloudComponentGroup(ComponentGroupModel model) {
		CallResult callResult = CallResult.newInstance();
		if (!StringUtils.hasLength(model.getId()) ) {
//			if("en".equals(model.getLanguage())){
//				callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT_EN );
//			}else{
//				callResult.setErr( ErrCodeExt.PARAM_ERR , ErrCodeExt.PARAM_LOSS_EXCEPTION_TEXT );
//			}
			TranslateLanguage.getCallResult(callResult,model.getLanguage(), ErrCodeExt.PARAM_LOSS_EXCEPTION);
			return callResult;
		}
		String powerStationId="";
		CloudTerminalModel cloudTerminalModel=null;
		if(CommonUtil.isEmpty(model.getCloudId())==true){
			cloudTerminalModel=cloudTerminalMapper.queryCloudTerminalModelById(model.getCloudId());
			if(cloudTerminalModel!=null){
				powerStationId=cloudTerminalModel.getPowerStationId();
			}
		}
		 String[] ids =  model.getId().split(",", -1);
		 Map<String ,Object> map2 = null;

		 for(int i=0;i<ids.length;i++)
		 {
			map2 = new HashMap<>();
			String id = ids[i].toString();
		    ComponentGroupModel cgm =	componentGroupMapper.queryComponentGroupById(id);
			 if(cgm!=null && cgm.getPowerStationId()!=null && cgm.getPowerStationId().length()>0){//删除缓存
				 Map<String, List<ComponentModel>> componentMap= Configure.getComponentMap();
				 componentMap.remove(cgm.getPowerStationId());
			 }
		    String cloudid =null;
		    String inverid = null;
		    String powerid = null;
			 if(CommonUtil.isEmpty(model.getCloudId())==true){//选择组串
				 powerid=powerStationId;
				 cloudid=model.getCloudId();
			 }else{//移除组串
				 inverid=cgm.getInverterId();
			 }
		    map2.put("id", id);
		    map2.put("inverterId", inverid);
		    map2.put("powerStationId", powerid);
		    map2.put("cloudId", cloudid);
		    int row  = componentGroupMapper.updateCloudExistsInverer(map2);
		    if(CommonUtil.isEmpty(model.getCloudId())==true) {//选择组串
			    //修改组件 的电站ID
			    Map<String ,Object> mapP = new HashMap<>();
				if(CommonUtil.isEmpty(powerid)==true ) {//采集器存在电站Id 组串不存在电站ID //&& CommonUtil.isEmpty(cgm.getPowerStationId())==false
					mapP.put("powerStationId",powerid);
				}
			    mapP.put("belongsGroupId",id);
			    if(cloudTerminalModel!=null){
				    mapP.put("imei",cloudTerminalModel.getImei());
				    mapP.put("cloudId",cloudTerminalModel.getId());
			    }
			    componentMapper.updatePowerStationByGroupId(mapP);
			}else{
				Map<String ,Object> mapP = new HashMap<>();
				mapP.put("belongsGroupId",id);
			    mapP.put("cloudIdFlag","-1");
			    if(cgm.getInverterId() !=null && cgm.getInverterId().length()>0){
				    InverterModel inverterModel=inverterMapper.queryInverterById(cgm.getInverterId(),null);
				    if(inverterModel.getPowerStationId()!=null && inverterModel.getPowerStationId().length()>0){
					    mapP.put("powerStationId",inverterModel.getPowerStationId());
				    }
			    }
				componentMapper.updatePowerStationByGroupId(mapP);
			}
		 }
		return callResult;
	}

    /**
     * 查询所有的组串
     * @return
     */
    public List<ComponentGroupModel> queryComponentGroupList() {
        Map<String , Object > map = new HashMap<>();
        List<ComponentGroupModel> list = componentGroupMapper.queryComponentGroupList(map);
        if (!list.isEmpty()) {
            CommonUtil.hanldList(list);
        }
        return list;
    }

	@Override
	public List<ComponentGroupModel> queryComponentGroupListByData(Map<String, Object> map) {
		return componentGroupMapper.queryComponentGroupListByData(map);
	}


	@Override
	public String validatecomponentGroup(String id,String num, String model, String serialNo, String chipId) {
		Map<String , Object> map = new HashMap<>();
		String message = null;
		map.put("groupNo", num);
		map.put("id", id);

		List<ComponentGroupModel> lt = componentGroupMapper.queryvalidatecomponentGroup(map);
		
		if(lt.size()>0)
		{
			message="组串编号重复，请重新输入！";
		}
		
		if(message == null)
		{
			message = "success";
		}
		return message;
	}

	@Override
	public List<ComponentGroupModel> queryListByCloudId(String id) {
		return componentGroupMapper.queryListByCloudId(id);
	}

	@Override
	public List<ComponentGroupModel> queryListByInverterId(String id) {
		return componentGroupMapper.queryListByInverterId(id);
	}

	@Override
	public List<ComponentGroupModel> querycomponentGroupByPowerId(String powerid) {
		System.out.println(powerid);
		return componentGroupMapper.querycomponentGroupByPowerId(powerid);
	}

	@Override
	public String importGroupByExcel(HttpServletRequest request, HttpServletResponse response)
		{
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			MultipartFile file = multipartRequest.getFile("upfile");
			if (file.isEmpty()) {
				return "upload failed";
			}

			InputStream in;
			List<List<Object>> listObject = null;
			try {
				in = file.getInputStream();
				// 读取excel的内容
				listObject = new ExcelUtils().getBankListByExcel(in, file.getOriginalFilename());
			} catch (Exception e) {
				e.printStackTrace();
			}

			if (listObject == null || listObject.size() == 0) {
				return "upload failed";
			}

			HttpSession session = request.getSession();
			String userId = session.getAttribute("userKey").toString();

			List<Object> oneRow;
			ComponentGroupModel model;

			for (List<Object> objects : listObject) {
				// 取出一行数据
				oneRow = objects;
				model = new ComponentGroupModel();
				//组串名称
				model.setGroupName(oneRow.get(0).toString());
				//组串类型
				model.setGroupType(Integer.valueOf(oneRow.get(1).toString()));
				//组串功率
				model.setPower(Integer.valueOf(oneRow.get(2).toString()));
				//组串标识
				model.setGroupNo(oneRow.get(3).toString());
				//创建者id
				model.setCreateUserId(userId);
				//组串id
				model.setId(IDUtil.getUUIDStr());
				componentGroupMapper.saveComponentGroupModel(model);
			}
			// 插入位置信息

			return "upload Success";
		}
	@Override
	public List<GroupPowerModel> queryGroupIdAndName(String powerStationId, String groupName) {
		return componentGroupMapper.queryGroupIdAndName(powerStationId,groupName);
	}

	@Override
	public List<ComponentGroupModel> queryGroupPosition(String powerStationId) {
		return componentGroupMapper.queryGroupPosition(powerStationId);
	}

	@Override
	public List<ShowGroupModel> queryGroupPositionForApp(String powerStationId) {
		return componentGroupMapper.queryGroupPositionForApp(powerStationId);
	}

	@Override
	public List<GroupModel> queryGroupInfoForApp(String powerStationId)
	{
		return componentGroupMapper.queryGroupInfoForApp(powerStationId);
	}

	@Override
	public List<GroupModel> queryGroupInfoForWeb(String powerStationId)
	{
		return componentGroupMapper.queryGroupInfoForWeb(powerStationId);
	}

	@Override
	public GroupModel queryGroupInfo(Map<String, Object> map) {
		return componentGroupMapper.queryGroupInfo(map);
	}

	@Override
	public int saveGroupModel(GroupModel gm1) {
		ComponentGroupModel cgm = new ComponentGroupModel();
		/*cgm.setGroupName(gm1.getGroupName());
		cgm.setDivtop(gm1.getDivtop());*/
		//BeanUtils.copyProperties(gm1,cgm);
		/*String id = IDUtil.getUUIDStr();
		cgm.setId(id);*/
		cgm.setId(gm1.getGroupId());
		cgm.setGroupName(gm1.getGroupName());
		cgm.setPowerStationId(gm1.getPowerStationId());
		return componentGroupMapper.saveComponentGroupModel(cgm);
	}


	@Override
	public int saveGroupPosition(GroupModel gm1) {
		return componentGroupMapper.saveGroupPosition(gm1);
	}

	@Override
	public int updateGroupPosition(GroupModel gm2) {
		return componentGroupMapper.updateGroupPosition(gm2);
	}

	@Override
	public String queryGroupById(String groupId) {
		return componentGroupMapper.queryGroupById(groupId);
	}
}
